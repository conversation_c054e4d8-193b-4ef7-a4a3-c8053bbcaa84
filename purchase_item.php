<?php
session_start();
require_once "db.php";

// Enable error logging
error_reporting(E_ALL);
ini_set('display_errors', 1);

header('Content-Type: application/json');

// Check if user is logged in
if (!isset($_SESSION["username"])) {
    http_response_code(401);
    echo json_encode(['success' => false, 'message' => 'Not logged in']);
    exit;
}

// Get JSON input
$input = json_decode(file_get_contents('php://input'), true);

if (!$input || !isset($input['item_id'], $input['price'], $input['quantity'], $input['recipient'])) {
    http_response_code(400);
    echo json_encode(['success' => false, 'message' => 'Invalid request data']);
    exit;
}

$itemId = intval($input['item_id']);
$expectedPrice = intval($input['price']);
$expectedQuantity = intval($input['quantity']);
$recipient = $input['recipient'];

try {
    // Get account info
    $accountQuery = "SELECT id, name FROM account_data WHERE name = ?";
    $stmt = $conn->prepare($accountQuery);
    $stmt->bind_param("s", $_SESSION["username"]);
    $stmt->execute();
    $accountResult = $stmt->get_result();
    $account = $accountResult->fetch_assoc();

    if (!$account) {
        throw new Exception("Account not found");
    }

    $userId = $account['id'];
    $accountName = $account['name'];

    // Get item details
    $itemQuery = "SELECT id, item_id, name, price, quantity FROM shop_items WHERE id = ?";
    $stmt = $conn->prepare($itemQuery);
    $stmt->bind_param("i", $itemId);
    $stmt->execute();
    $itemResult = $stmt->get_result();
    $item = $itemResult->fetch_assoc();

    if (!$item) {
        throw new Exception("Item not found");
    }

    // Verify price and quantity
    if ($item['price'] != $expectedPrice) {
        throw new Exception("Price mismatch");
    }

    if ($item['quantity'] != $expectedQuantity) {
        throw new Exception("Quantity mismatch");
    }

    // Check user balance
    $balanceQuery = "SELECT value FROM account_balance WHERE account_id = ? AND price_id = 1";
    $stmt = $conn->prepare($balanceQuery);
    $stmt->bind_param("i", $userId);
    $stmt->execute();
    $balanceResult = $stmt->get_result();
    $balance = $balanceResult->fetch_assoc();
    $userPoints = $balance['value'] ?? 0;

    if ($userPoints < $item['price']) {
        throw new Exception("Insufficient points");
    }

    // Get character info
    $characterId = $recipient['character_id'];
    $playerQuery = "SELECT id, name FROM players WHERE id = ? AND account_id = ?";
    $stmt = $conn->prepare($playerQuery);
    $stmt->bind_param("ii", $characterId, $userId);
    $stmt->execute();
    $playerResult = $stmt->get_result();
    $player = $playerResult->fetch_assoc();

    if (!$player) {
        throw new Exception("Character not found or does not belong to your account");
    }

    $playerId = $player['id'];
    $playerName = $player['name'];

    // Start transaction
    $conn->begin_transaction();

    // Get next unique IDs safely
    $itemUniqueIdResult = $conn->query("SELECT COALESCE(MAX(itemUniqueId), 0) + 1 as next_id FROM inventory");
    $newItemUniqueId = $itemUniqueIdResult ? $itemUniqueIdResult->fetch_assoc()['next_id'] : 1;

    $mailUniqueIdResult = $conn->query("SELECT COALESCE(MAX(mailUniqueId), 0) + 1 as next_id FROM mail");
    $newMailId = $mailUniqueIdResult ? $mailUniqueIdResult->fetch_assoc()['next_id'] : 1;

    // Insert into inventory (using correct column names)
    $inventoryQuery = "INSERT INTO inventory (itemUniqueId, itemId, itemCount, itemOwner, itemLocation, itemColor, isEquiped, isSoulBound, slot, enchant, itemCreator, itemSkin, fusionedItem, optionalSocket, optionalFusionSocket, conditioning) VALUES (?, ?, ?, ?, 127, 0, 0, 0, 0, 0, '', 0, 0, 0, 0, 0)";
    $stmt = $conn->prepare($inventoryQuery);
    
    // Fix the binding issue by using variables
    $gameItemId = $item['item_id'];
    $itemQuantity = $item['quantity'];
    $stmt->bind_param("iiii", $newItemUniqueId, $gameItemId, $itemQuantity, $playerId);
    
    if (!$stmt->execute()) {
        throw new Exception("Failed to create item: " . $stmt->error);
    }

    // Insert mail (using correct column names)
    $mailQuery = "INSERT INTO mail (mailUniqueId, mailRecipientId, senderName, mailTitle, mailMessage, attachedItemId, attachedKinahCount, unread, express, recievedTime) VALUES (?, ?, ?, ?, ?, ?, 0, 1, 1, NOW())";
    $stmt = $conn->prepare($mailQuery);
    
    $mailSender = "Donation Shop";
    $mailTitle = "Item Purchase";
    $mailMessage = "You have purchased: " . $item['name'] . " (x" . $item['quantity'] . ") for " . $item['price'] . " DP. Enjoy your item!";
    
    $stmt->bind_param("iisssi", $newMailId, $playerId, $mailSender, $mailTitle, $mailMessage, $newItemUniqueId);
    
    if (!$stmt->execute()) {
        throw new Exception("Failed to send mail: " . $stmt->error);
    }

    // Deduct points from balance
    $deductQuery = "UPDATE account_balance SET value = value - ? WHERE account_id = ? AND price_id = 1";
    $stmt = $conn->prepare($deductQuery);
    $itemPrice = $item['price'];
    $stmt->bind_param("ii", $itemPrice, $userId);
    
    if (!$stmt->execute()) {
        throw new Exception("Failed to deduct points: " . $stmt->error);
    }

    // Add transaction history
    $historyQuery = "INSERT INTO balance_history (date, reference_id, account_id, account_name, price_id, amount, description, type) VALUES (NOW(), ?, ?, ?, 1, ?, ?, 'OUT')";
    $orderRef = 'SHOP_' . $userId . '_' . time();
    $description = 'Purchased: ' . $item['name'] . ' (x' . $item['quantity'] . ')';
    $stmt = $conn->prepare($historyQuery);
    $stmt->bind_param("ssiss", $orderRef, $userId, $accountName, $itemPrice, $description);
    
    if (!$stmt->execute()) {
        throw new Exception("Failed to record transaction: " . $stmt->error);
    }

    // Commit transaction
    $conn->commit();

    // Success response
    echo json_encode([
        'success' => true,
        'message' => 'Purchase successful! Item sent to character "' . $playerName . '". Check your in-game mail.',
        'remaining_points' => $userPoints - $item['price'],
        'character_name' => $playerName,
        'mail_id' => $newMailId,
        'item_unique_id' => $newItemUniqueId
    ]);

} catch (Exception $e) {
    // Rollback transaction on error
    if (isset($conn)) {
        $conn->rollback();
    }
    
    echo json_encode([
        'success' => false,
        'message' => 'Purchase failed: ' . $e->getMessage()
    ]);
}

$conn->close();
?>
