<?php
session_start();
require_once "db.php";

// Enable error logging
error_reporting(E_ALL);
ini_set('display_errors', 1);
error_log("Purchase attempt started");

header('Content-Type: application/json');

// Check if user is logged in
if (!isset($_SESSION["username"])) {
    http_response_code(401);
    echo json_encode(['success' => false, 'message' => 'Not logged in']);
    exit;
}

// Get JSON input
$input = json_decode(file_get_contents('php://input'), true);

if (!$input || !isset($input['item_id'], $input['price'], $input['quantity'], $input['recipient'])) {
    http_response_code(400);
    echo json_encode(['success' => false, 'message' => 'Invalid request data']);
    exit;
}

$itemId = intval($input['item_id']);
$expectedPrice = intval($input['price']);
$expectedQuantity = intval($input['quantity']);
$recipient = $input['recipient'];

// Get account info
$accountQuery = "SELECT id, name FROM account_data WHERE name = ?";
$stmt = $conn->prepare($accountQuery);
$stmt->bind_param("s", $_SESSION["username"]);
$stmt->execute();
$accountResult = $stmt->get_result();
$account = $accountResult->fetch_assoc();

if (!$account) {
    http_response_code(404);
    echo json_encode(['success' => false, 'message' => 'Account not found']);
    exit;
}

$userId = $account['id'];
$accountName = $account['name'];

// Get item details
$itemQuery = "SELECT id, item_id, name, price, quantity FROM shop_items WHERE id = ?";
$stmt = $conn->prepare($itemQuery);
$stmt->bind_param("i", $itemId);
$stmt->execute();
$itemResult = $stmt->get_result();
$item = $itemResult->fetch_assoc();

if (!$item) {
    echo json_encode(['success' => false, 'message' => 'Item not found']);
    exit;
}

// Verify price and quantity
if ($item['price'] != $expectedPrice) {
    echo json_encode(['success' => false, 'message' => 'Price mismatch']);
    exit;
}

if ($item['quantity'] != $expectedQuantity) {
    echo json_encode(['success' => false, 'message' => 'Quantity mismatch']);
    exit;
}

// Check user balance
$balanceQuery = "SELECT value FROM account_balance WHERE account_id = ? AND price_id = 1";
$stmt = $conn->prepare($balanceQuery);
$stmt->bind_param("i", $userId);
$stmt->execute();
$balanceResult = $stmt->get_result();
$balance = $balanceResult->fetch_assoc();
$userPoints = $balance['value'] ?? 0;

if ($userPoints < $item['price']) {
    echo json_encode(['success' => false, 'message' => 'Insufficient points']);
    exit;
}

// Handle recipient selection (self or gift)
if ($recipient['type'] === 'self') {
    // Send to buyer's own character
    $selectedCharacterId = intval($recipient['character_id']);

    // Verify the selected character belongs to this account
    $playersQuery = "SELECT id, name FROM players WHERE id = ? AND account_id = ?";
    $stmt = $conn->prepare($playersQuery);
    $stmt->bind_param("ii", $selectedCharacterId, $userId);
    $stmt->execute();
    $playersResult = $stmt->get_result();

    if ($playersResult->num_rows == 0) {
        echo json_encode(['success' => false, 'message' => 'Selected character not found or does not belong to your account.']);
        exit;
    }

    $player = $playersResult->fetch_assoc();
    $playerId = $player['id'];
    $playerName = $player['name'];
    $isGift = false;

} else {
    // Gift to another player's character
    $giftCharacterName = trim($recipient['character_name']);

    if (empty($giftCharacterName)) {
        echo json_encode(['success' => false, 'message' => 'Recipient character name is required for gifts.']);
        exit;
    }

    // Find the character by name (case-insensitive for better UX)
    $playersQuery = "SELECT id, name, account_id FROM players WHERE LOWER(name) = LOWER(?)";
    $stmt = $conn->prepare($playersQuery);
    $stmt->bind_param("s", $giftCharacterName);
    $stmt->execute();
    $playersResult = $stmt->get_result();

    // Debug: Log the search attempt
    error_log("Searching for character: '$giftCharacterName', Found: " . $playersResult->num_rows . " results");

    if ($playersResult->num_rows == 0) {
        // Debug: Show similar character names to help with troubleshooting
        $similarQuery = "SELECT name FROM players WHERE name LIKE ? LIMIT 5";
        $stmt = $conn->prepare($similarQuery);
        $searchPattern = '%' . $giftCharacterName . '%';
        $stmt->bind_param("s", $searchPattern);
        $stmt->execute();
        $similarResult = $stmt->get_result();

        $suggestions = [];
        while ($row = $similarResult->fetch_assoc()) {
            $suggestions[] = $row['name'];
        }

        $message = "Character '$giftCharacterName' not found. Please check the spelling.";
        if (!empty($suggestions)) {
            $message .= " Similar names: " . implode(', ', $suggestions);
        }

        echo json_encode(['success' => false, 'message' => $message]);
        exit;
    }

    $player = $playersResult->fetch_assoc();
    $playerId = $player['id'];
    $playerName = $player['name'];
    $recipientAccountId = $player['account_id'];
    $isGift = true;

    // Prevent gifting to yourself
    if ($recipientAccountId == $userId) {
        echo json_encode(['success' => false, 'message' => 'You cannot gift items to your own characters. Use "Send to my character" instead.']);
        exit;
    }
}

// Start transaction
$conn->begin_transaction();

try {
    // Deduct points from balance
    $deductQuery = "UPDATE account_balance SET value = value - ? WHERE account_id = ? AND price_id = 1";
    $stmt = $conn->prepare($deductQuery);
    $stmt->bind_param("ii", $item['price'], $userId);
    $stmt->execute();
    
    // Add transaction history
    $historyQuery = "INSERT INTO balance_history (date, reference_id, account_id, account_name, price_id, amount, description, type) VALUES (NOW(), ?, ?, ?, 1, ?, ?, 'OUT')";
    $stmt = $conn->prepare($historyQuery);
    $orderRef = 'SHOP_' . $userId . '_' . time();

    if ($isGift) {
        $description = 'Gift to ' . $playerName . ': ' . $item['name'];
    } else {
        $description = 'Purchased: ' . $item['name'];
    }

    $stmt->bind_param("ssiss", $orderRef, $userId, $accountName, $item['price'], $description);
    $stmt->execute();
    
    // Add item to inventory and mail (same method as daily rewards)
    // Get next unique IDs safely
    $itemUniqueIdResult = $conn->query("SELECT COALESCE(MAX(item_unique_id), 0) + 1 as next_id FROM inventory");
    $newItemUniqueId = $itemUniqueIdResult ? $itemUniqueIdResult->fetch_assoc()['next_id'] : 1;

    $mailUniqueIdResult = $conn->query("SELECT COALESCE(MAX(mail_unique_id), 0) + 1 as next_id FROM mail");
    $newMailId = $mailUniqueIdResult ? $mailUniqueIdResult->fetch_assoc()['next_id'] : 1;
    
    // Insert item directly into inventory (location 127 = mail attachment) with correct quantity
    $inventoryQuery = "INSERT INTO inventory (item_unique_id, item_id, item_count, item_owner, item_location) VALUES (?, ?, ?, ?, 127)";
    $stmt = $conn->prepare($inventoryQuery);
    $stmt->bind_param("iiii", $newItemUniqueId, $item['item_id'], $item['quantity'], $playerId);

    error_log("Inserting inventory - ItemUniqueID: $newItemUniqueId, ItemID: {$item['item_id']}, Count: {$item['quantity']}, Owner: $playerId");

    if (!$stmt->execute()) {
        throw new Exception("Failed to insert inventory item: " . $stmt->error);
    }

    if ($stmt->affected_rows == 0) {
        throw new Exception("Inventory insert affected 0 rows");
    }

    error_log("Inventory item inserted successfully");
    
    // Insert mail with item attached
    if ($isGift) {
        $mailSender = substr('Gift from ' . $accountName, 0, 20); // Limit sender to 20 chars
        $mailTitle = 'Gift'; // Keep title short and simple
        $mailMessage = 'You have received a gift from ' . $accountName . '! Item: ' . $item['name'] . ' (Quantity: ' . $item['quantity'] . ')';
    } else {
        $mailSender = 'Donation Shop';
        $mailTitle = 'Shop Purchase';
        $mailMessage = 'Thank you for your purchase! Here is your item: ' . $item['name'] . ' (Quantity: ' . $item['quantity'] . ')';
    }

    $mailQuery = "INSERT INTO mail (mail_unique_id, mail_recipient_id, sender_name, mail_title, mail_message, attached_item_id, attached_kinah_count, unread, express) VALUES (?, ?, ?, ?, ?, ?, 0, 1, 1)";
    $stmt = $conn->prepare($mailQuery);
    $stmt->bind_param("iisssi", $newMailId, $playerId, $mailSender, $mailTitle, $mailMessage, $newItemUniqueId);

    // Debug: Log mail insertion details
    error_log("Inserting mail - ID: $newMailId, Recipient: $playerId ($playerName), Sender: $mailSender, Item: $newItemUniqueId");
    error_log("Mail values - newMailId: " . var_export($newMailId, true) . ", playerId: " . var_export($playerId, true) . ", newItemUniqueId: " . var_export($newItemUniqueId, true));

    if (!$stmt->execute()) {
        throw new Exception("Failed to insert mail: " . $stmt->error);
    }

    if ($stmt->affected_rows == 0) {
        throw new Exception("Mail insert affected 0 rows");
    }

    error_log("Mail inserted successfully");
    
    // Commit transaction
    $conn->commit();
    
    if ($isGift) {
        $successMessage = "Gift sent successfully! Item delivered to '$playerName' (ID: $playerId). They will receive it in their in-game mail. Mail ID: $newMailId, Item ID: $newItemUniqueId";
    } else {
        $successMessage = "Purchase successful! Item sent to character '$playerName'. Check your in-game mail.";
    }

    echo json_encode([
        'success' => true,
        'message' => $successMessage,
        'remaining_points' => $userPoints - $item['price'],
        'character_name' => $playerName,
        'is_gift' => $isGift
    ]);
    
} catch (Exception $e) {
    // Rollback transaction on error
    $conn->rollback();
    error_log("Shop purchase error: " . $e->getMessage());
    echo json_encode([
        'success' => false,
        'message' => 'Purchase failed: ' . $e->getMessage()
    ]);
}

$conn->close();
?>
